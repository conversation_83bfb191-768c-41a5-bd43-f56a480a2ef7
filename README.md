# Nikkou

A Flutter delivery app with a clean architecture.

## Features

- User Authentication (Phone + OTP)
- Order Management
- Real-time Order Tracking
- User Profile Management
- Customer Support

## Architecture

The app follows a feature-based architecture with the following structure:

```
lib/
├── core/
│   ├── theme/          # App theming (colors, typography, theme)
│   └── widgets/        # Reusable UI components
└── features/
    ├── splash/         # Splash screen
    ├── auth/           # Authentication (login, OTP)
    ├── home/           # Home screen with navigation
    ├── orders/         # Order management
    ├── tracking/       # Order tracking
    ├── profile/        # User profile
    └── support/        # Customer support
```

## Getting Started

1. Ensure you have Flutter installed
2. Clone this repository
3. Run `flutter pub get` to install dependencies
4. Run `flutter run` to start the app

## Dependencies

- flutter: SDK
- cupertino_icons: iOS-style icons

## Development

This project uses:
- Material Design 3
- Custom theme system
- Reusable widget components
- Feature-based architecture
