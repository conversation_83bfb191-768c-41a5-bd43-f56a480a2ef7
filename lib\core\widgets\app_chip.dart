import 'package:flutter/material.dart';
import '../theme/app_colors.dart';
import '../theme/app_typography.dart';

class AppChip extends StatelessWidget {
  final String label;
  final VoidCallback? onTap;
  final VoidCallback? onDeleted;
  final Color? backgroundColor;
  final Color? textColor;
  final Widget? avatar;
  final Widget? deleteIcon;
  final bool selected;
  final EdgeInsetsGeometry? padding;

  const AppChip({
    super.key,
    required this.label,
    this.onTap,
    this.onDeleted,
    this.backgroundColor,
    this.textColor,
    this.avatar,
    this.deleteIcon,
    this.selected = false,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    if (onTap != null) {
      return ActionChip(
        label: Text(
          label,
          style: AppTypography.caption.copyWith(
            color: textColor ?? (selected ? AppColors.white : AppColors.textPrimary),
          ),
        ),
        onPressed: onTap,
        backgroundColor: backgroundColor ?? (selected ? AppColors.primary : AppColors.greyLight),
        avatar: avatar,
        deleteIcon: deleteIcon,
        onDeleted: onDeleted,
        padding: padding ?? const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
      );
    }

    return Chip(
      label: Text(
        label,
        style: AppTypography.caption.copyWith(
          color: textColor ?? (selected ? AppColors.white : AppColors.textPrimary),
        ),
      ),
      backgroundColor: backgroundColor ?? (selected ? AppColors.primary : AppColors.greyLight),
      avatar: avatar,
      deleteIcon: deleteIcon,
      onDeleted: onDeleted,
      padding: padding ?? const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
    );
  }
}
