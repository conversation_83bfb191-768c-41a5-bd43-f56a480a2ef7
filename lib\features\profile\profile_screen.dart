import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_typography.dart';
import '../../core/widgets/app_appbar.dart';
import '../../core/widgets/app_card.dart';
import '../../core/widgets/app_avatar.dart';
import '../../core/widgets/app_button.dart';
import '../auth/login_screen.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppAppBar(
        title: 'Profile',
        automaticallyImplyLeading: false,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Profile header
            AppCard(
              child: Column(
                children: [
                  const AppAvatar(
                    initials: 'UN',
                    radius: 40,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'User Name',
                    style: AppTypography.h5,
                  ),
                  Text(
                    '+91 9876543210',
                    style: AppTypography.bodyMedium.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 16),
                  AppButton(
                    text: 'Edit Profile',
                    onPressed: () {
                      // Navigate to edit profile
                    },
                    isOutlined: true,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            
            // Profile options
            AppCard(
              child: Column(
                children: [
                  _buildProfileOption(
                    icon: Icons.location_on,
                    title: 'Saved Addresses',
                    subtitle: 'Manage your delivery addresses',
                    onTap: () {
                      // Navigate to addresses
                    },
                  ),
                  const Divider(),
                  _buildProfileOption(
                    icon: Icons.payment,
                    title: 'Payment Methods',
                    subtitle: 'Manage your payment options',
                    onTap: () {
                      // Navigate to payment methods
                    },
                  ),
                  const Divider(),
                  _buildProfileOption(
                    icon: Icons.notifications,
                    title: 'Notifications',
                    subtitle: 'Manage notification preferences',
                    onTap: () {
                      // Navigate to notifications settings
                    },
                  ),
                  const Divider(),
                  _buildProfileOption(
                    icon: Icons.help,
                    title: 'Help & Support',
                    subtitle: 'Get help and contact support',
                    onTap: () {
                      // Navigate to help
                    },
                  ),
                  const Divider(),
                  _buildProfileOption(
                    icon: Icons.info,
                    title: 'About',
                    subtitle: 'App version and information',
                    onTap: () {
                      // Show about dialog
                    },
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            
            // Logout button
            AppCard(
              child: _buildProfileOption(
                icon: Icons.logout,
                title: 'Logout',
                subtitle: 'Sign out of your account',
                textColor: AppColors.error,
                onTap: () {
                  _showLogoutDialog(context);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    Color? textColor,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: textColor ?? AppColors.primary,
      ),
      title: Text(
        title,
        style: AppTypography.bodyMedium.copyWith(
          color: textColor,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: AppTypography.bodySmall,
      ),
      trailing: const Icon(
        Icons.chevron_right,
        color: AppColors.grey,
      ),
      onTap: onTap,
      contentPadding: EdgeInsets.zero,
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Logout',
          style: AppTypography.h6,
        ),
        content: Text(
          'Are you sure you want to logout?',
          style: AppTypography.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: AppTypography.button.copyWith(
                color: AppColors.grey,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pushAndRemoveUntil(
                MaterialPageRoute(builder: (context) => const LoginScreen()),
                (route) => false,
              );
            },
            child: Text(
              'Logout',
              style: AppTypography.button.copyWith(
                color: AppColors.error,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
