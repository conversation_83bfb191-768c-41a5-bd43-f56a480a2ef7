import 'package:flutter/material.dart';
import '../theme/app_colors.dart';
import '../theme/app_typography.dart';

class AppAvatar extends StatelessWidget {
  final String? imageUrl;
  final String? initials;
  final double radius;
  final Color? backgroundColor;
  final Color? textColor;
  final VoidCallback? onTap;
  final Widget? child;

  const AppAvatar({
    super.key,
    this.imageUrl,
    this.initials,
    this.radius = 20,
    this.backgroundColor,
    this.textColor,
    this.onTap,
    this.child,
  });

  @override
  Widget build(BuildContext context) {
    Widget avatar = CircleAvatar(
      radius: radius,
      backgroundColor: backgroundColor ?? AppColors.primary,
      backgroundImage: imageUrl != null ? NetworkImage(imageUrl!) : null,
      child: child ??
          (imageUrl == null
              ? Text(
                  initials ?? '?',
                  style: AppTypography.bodyMedium.copyWith(
                    color: textColor ?? AppColors.white,
                    fontWeight: FontWeight.w600,
                  ),
                )
              : null),
    );

    if (onTap != null) {
      return GestureDetector(
        onTap: onTap,
        child: avatar,
      );
    }

    return avatar;
  }
}

class AppAvatarWithBadge extends StatelessWidget {
  final AppAvatar avatar;
  final Widget badge;
  final Alignment badgeAlignment;

  const AppAvatarWithBadge({
    super.key,
    required this.avatar,
    required this.badge,
    this.badgeAlignment = Alignment.topRight,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        avatar,
        Positioned.fill(
          child: Align(
            alignment: badgeAlignment,
            child: badge,
          ),
        ),
      ],
    );
  }
}
