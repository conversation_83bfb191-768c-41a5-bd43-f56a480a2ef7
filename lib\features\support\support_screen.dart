import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_typography.dart';
import '../../core/widgets/app_appbar.dart';
import '../../core/widgets/app_card.dart';
import '../../core/widgets/app_text_field.dart';
import '../../core/widgets/app_button.dart';

class SupportScreen extends StatefulWidget {
  const SupportScreen({super.key});

  @override
  State<SupportScreen> createState() => _SupportScreenState();
}

class _SupportScreenState extends State<SupportScreen> {
  final _formKey = GlobalKey<FormState>();
  final _subjectController = TextEditingController();
  final _messageController = TextEditingController();
  bool _isSubmitting = false;

  @override
  void dispose() {
    _subjectController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  void _submitTicket() {
    if (_formKey.currentState?.validate() ?? false) {
      setState(() {
        _isSubmitting = true;
      });

      // Simulate API call
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          setState(() {
            _isSubmitting = false;
          });
          
          // Clear form
          _subjectController.clear();
          _messageController.clear();
          
          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Support ticket submitted successfully'),
              backgroundColor: AppColors.success,
            ),
          );
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppAppBar(title: 'Help & Support'),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Quick help options
            AppCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Quick Help',
                    style: AppTypography.h6,
                  ),
                  const SizedBox(height: 16),
                  _buildQuickHelpOption(
                    icon: Icons.phone,
                    title: 'Call Support',
                    subtitle: 'Speak with our support team',
                    onTap: () {
                      // Make phone call
                    },
                  ),
                  const Divider(),
                  _buildQuickHelpOption(
                    icon: Icons.chat,
                    title: 'Live Chat',
                    subtitle: 'Chat with our support team',
                    onTap: () {
                      // Open live chat
                    },
                  ),
                  const Divider(),
                  _buildQuickHelpOption(
                    icon: Icons.email,
                    title: 'Email Support',
                    subtitle: 'Send us an email',
                    onTap: () {
                      // Open email
                    },
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            
            // FAQ Section
            AppCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Frequently Asked Questions',
                    style: AppTypography.h6,
                  ),
                  const SizedBox(height: 16),
                  _buildFAQItem(
                    'How do I track my order?',
                    'You can track your order using the tracking number provided in the confirmation email or SMS.',
                  ),
                  _buildFAQItem(
                    'What are the delivery charges?',
                    'Delivery charges vary based on distance and package size. You can see the exact charges before placing an order.',
                  ),
                  _buildFAQItem(
                    'How do I cancel my order?',
                    'You can cancel your order from the order details page if it hasn\'t been picked up yet.',
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            
            // Contact form
            AppCard(
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Submit a Ticket',
                      style: AppTypography.h6,
                    ),
                    const SizedBox(height: 16),
                    AppTextField(
                      label: 'Subject',
                      hint: 'Brief description of your issue',
                      controller: _subjectController,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a subject';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    AppTextField(
                      label: 'Message',
                      hint: 'Describe your issue in detail',
                      controller: _messageController,
                      maxLines: 5,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your message';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    AppButton(
                      text: 'Submit Ticket',
                      onPressed: _submitTicket,
                      isLoading: _isSubmitting,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickHelpOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: AppColors.primary,
      ),
      title: Text(
        title,
        style: AppTypography.bodyMedium,
      ),
      subtitle: Text(
        subtitle,
        style: AppTypography.bodySmall,
      ),
      trailing: const Icon(
        Icons.chevron_right,
        color: AppColors.grey,
      ),
      onTap: onTap,
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildFAQItem(String question, String answer) {
    return ExpansionTile(
      title: Text(
        question,
        style: AppTypography.bodyMedium,
      ),
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Text(
            answer,
            style: AppTypography.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ),
      ],
      tilePadding: EdgeInsets.zero,
      childrenPadding: EdgeInsets.zero,
    );
  }
}
