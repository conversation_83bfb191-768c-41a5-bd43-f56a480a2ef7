{"roots": ["nikkou"], "packages": [{"name": "nikkou", "version": "1.0.0+1", "dependencies": ["cupertino_icons", "flutter"], "devDependencies": ["flutter_lints", "flutter_test"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["clock", "collection", "fake_async", "flutter", "leak_tracker_flutter_testing", "matcher", "meta", "path", "stack_trace", "stream_channel", "test_api", "vector_math"]}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "vector_math", "version": "2.2.0", "dependencies": []}, {"name": "test_api", "version": "0.7.6", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.10", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "leak_tracker", "version": "11.0.1", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "leak_tracker_testing", "version": "3.0.2", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "flutter_lints", "version": "2.0.3", "dependencies": ["lints"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "lints", "version": "2.1.1", "dependencies": []}, {"name": "cupertino_icons", "version": "1.0.8", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "vm_service", "version": "15.0.2", "dependencies": []}], "configVersion": 1}