import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_typography.dart';
import '../../core/widgets/app_appbar.dart';
import '../../core/widgets/app_card.dart';
import '../../core/widgets/app_text_field.dart';
import '../../core/widgets/app_button.dart';
import '../../core/widgets/app_chip.dart';

class TrackingScreen extends StatefulWidget {
  const TrackingScreen({super.key});

  @override
  State<TrackingScreen> createState() => _TrackingScreenState();
}

class _TrackingScreenState extends State<TrackingScreen> {
  final _trackingController = TextEditingController();
  bool _isTracking = false;
  bool _hasTrackingData = false;

  @override
  void dispose() {
    _trackingController.dispose();
    super.dispose();
  }

  void _trackOrder() {
    if (_trackingController.text.isNotEmpty) {
      setState(() {
        _isTracking = true;
      });

      // Simulate API call
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          setState(() {
            _isTracking = false;
            _hasTrackingData = true;
          });
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppAppBar(
        title: 'Track Order',
        automaticallyImplyLeading: false,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            AppCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    'Enter Tracking Number',
                    style: AppTypography.h6,
                  ),
                  const SizedBox(height: 16),
                  AppTextField(
                    hint: 'Enter order ID or tracking number',
                    controller: _trackingController,
                    prefixIcon: const Icon(Icons.search),
                  ),
                  const SizedBox(height: 16),
                  AppButton(
                    text: 'Track Order',
                    onPressed: _trackOrder,
                    isLoading: _isTracking,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            
            if (_hasTrackingData) ...[
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      AppCard(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Order #NK1001',
                                  style: AppTypography.h6,
                                ),
                                const AppChip(
                                  label: 'In Transit',
                                  backgroundColor: AppColors.warning,
                                  textColor: AppColors.white,
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            _buildTrackingStep(
                              'Order Placed',
                              'Your order has been placed successfully',
                              true,
                              true,
                            ),
                            _buildTrackingStep(
                              'Order Confirmed',
                              'Your order has been confirmed by the merchant',
                              true,
                              true,
                            ),
                            _buildTrackingStep(
                              'Picked Up',
                              'Your order has been picked up by our delivery partner',
                              true,
                              true,
                            ),
                            _buildTrackingStep(
                              'In Transit',
                              'Your order is on the way to destination',
                              true,
                              false,
                            ),
                            _buildTrackingStep(
                              'Delivered',
                              'Your order will be delivered soon',
                              false,
                              false,
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                      AppCard(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Delivery Details',
                              style: AppTypography.h6,
                            ),
                            const SizedBox(height: 12),
                            _buildDetailRow('From:', 'Sample Pickup Address'),
                            _buildDetailRow('To:', 'Sample Delivery Address'),
                            _buildDetailRow('Estimated Delivery:', 'Today, 6:00 PM'),
                            _buildDetailRow('Delivery Partner:', 'John Doe'),
                            _buildDetailRow('Contact:', '+91 9876543210'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ] else ...[
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.search,
                        size: 64,
                        color: AppColors.grey,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Enter tracking number to track your order',
                        style: AppTypography.bodyMedium.copyWith(
                          color: AppColors.textSecondary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTrackingStep(String title, String description, bool isCompleted, bool isActive) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isCompleted ? AppColors.success : 
                     isActive ? AppColors.primary : AppColors.grey,
            ),
            child: Icon(
              isCompleted ? Icons.check : Icons.circle,
              size: 16,
              color: AppColors.white,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTypography.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: isCompleted || isActive ? AppColors.textPrimary : AppColors.textSecondary,
                  ),
                ),
                Text(
                  description,
                  style: AppTypography.bodySmall.copyWith(
                    color: isCompleted || isActive ? AppColors.textSecondary : AppColors.textHint,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: AppTypography.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTypography.bodySmall,
            ),
          ),
        ],
      ),
    );
  }
}
